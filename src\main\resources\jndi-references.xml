<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">

    <!-- Development Datasources (without JNDI) -->
    <bean id="dataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <property name="driverClassName" value="oracle.jdbc.OracleDriver" />
        <property name="jdbcUrl" value="***********************************" />
        <property name="username" value="dev_user" />
        <property name="password" value="dev_password" />
        <property name="maximumPoolSize" value="10" />
        <property name="minimumIdle" value="2" />
    </bean>

    <bean id="postgresDataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <property name="driverClassName" value="org.postgresql.Driver" />
        <property name="jdbcUrl" value="***************************************" />
        <property name="username" value="dev_user" />
        <property name="password" value="dev_password" />
        <property name="maximumPoolSize" value="10" />
        <property name="minimumIdle" value="2" />
    </bean>

    <!-- MONGO CONNECTION (Production values) -->
    <bean id="mongoDBReplicaSet" class="java.lang.String">
        <constructor-arg value="ackbar.bnd.corp:27017;holdo.bnd.corp:27017;andor.bnd.corp:27017" />
    </bean>
    <bean id="mongoDBPort" class="java.lang.Integer">
        <constructor-arg value="27017" />
    </bean>
    <bean id="mongoDBName" class="java.lang.String">
        <constructor-arg value="callCenter" />
    </bean>
    <bean id="mongoAuthDB" class="java.lang.String">
        <constructor-arg value="admin" />
    </bean>
    <bean id="mongoDBUser" class="java.lang.String">
        <constructor-arg value="callcenter" />
    </bean>
    <bean id="mongoDBPass" class="java.lang.String">
        <constructor-arg value="callC3Nt37$" />
    </bean>

    <!--WEB REQUEST PROPERTIES (Development values)-->
    <bean id="redirectUrl" class="java.lang.String">
        <constructor-arg value="http://localhost:8080/callcenter/dev/redirect"/>
    </bean>
    <bean id="ciscoUrl" class="java.lang.String">
        <constructor-arg value="http://localhost:8080/callcenter/dev/cisco"/>
    </bean>
    <bean id="callBackCiscoUrl" class="java.lang.String">
        <constructor-arg value="http://localhost:8080/callcenter/dev/callback"/>
    </bean>
    <bean id="devMode" class="java.lang.Boolean">
        <constructor-arg value="true"/>
    </bean>

</beans>

